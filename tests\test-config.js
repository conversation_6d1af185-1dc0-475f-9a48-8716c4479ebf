/**
 * Test Configuration
 * Configures test environment to avoid API rate limiting and external dependencies
 */

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.GUIDANT_TEST_MODE = 'true';
process.env.GUIDANT_DISABLE_AI = 'true'; // Disable AI calls in tests
process.env.GUIDANT_DISABLE_EXTERNAL_APIS = 'true';

// Mock AI providers to avoid rate limiting
const mockAIResponse = {
  success: true,
  data: {
    insights: ['Mock insight 1', 'Mock insight 2'],
    analysis: 'Mock analysis result',
    recommendations: ['Mock recommendation']
  },
  metadata: {
    provider: 'mock',
    model: 'test-model',
    tokens: 100
  }
};

// Mock AI integration module
const originalAIIntegration = await import('../src/ai-integration/index.js').catch(() => null);

if (originalAIIntegration) {
  // Override AI methods with mocks
  const mockAI = {
    async enhanceInsights() {
      return mockAIResponse;
    },
    
    async analyzeContent() {
      return mockAIResponse;
    },
    
    async generateRecommendations() {
      return mockAIResponse;
    },
    
    async validateModel() {
      return { valid: true, provider: 'mock' };
    }
  };
  
  // Replace AI integration exports
  Object.assign(originalAIIntegration, mockAI);
}

// Mock external API calls
global.fetch = async (url, options) => {
  console.log(`Mock fetch called for: ${url}`);
  
  // Return mock responses for different APIs
  if (url.includes('openrouter.ai')) {
    return {
      ok: false,
      status: 429,
      statusText: 'Too Many Requests (Mocked)',
      json: async () => ({ error: 'Rate limited (test mock)' })
    };
  }
  
  return {
    ok: true,
    status: 200,
    json: async () => mockAIResponse
  };
};

// Reduce timeouts for faster tests
const originalSetTimeout = global.setTimeout;
global.setTimeout = (fn, delay) => {
  // Reduce delays in tests
  const testDelay = Math.min(delay, 100);
  return originalSetTimeout(fn, testDelay);
};

// Mock file system operations that might fail
import fs from 'fs/promises';
const originalReadFile = fs.readFile;

fs.readFile = async (path, options) => {
  try {
    return await originalReadFile(path, options);
  } catch (error) {
    if (error.code === 'ENOENT' && path.includes('.guidant')) {
      // Return mock data for missing Guidant files
      if (path.includes('config.json')) {
        return JSON.stringify({
          name: 'Test Project',
          type: 'web_app',
          version: '1.0.0'
        });
      }
      if (path.includes('phases.json')) {
        return JSON.stringify({
          current: 'requirements',
          phases: {
            concept: { status: 'completed' },
            requirements: { status: 'in_progress' }
          }
        });
      }
    }
    throw error;
  }
};

console.log('🧪 Test configuration loaded - AI calls disabled, timeouts reduced');

export default {
  testMode: true,
  disableAI: true,
  disableExternalAPIs: true,
  mockResponses: {
    ai: mockAIResponse
  }
};
